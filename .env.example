# Database Configuration
# Options: sqlite, postgresql
DATABASE_TYPE=sqlite

# SQLite Configuration (used when DATABASE_TYPE=sqlite)
SQLITE_DB_PATH=data/novels.db

# PostgreSQL Configuration (used when DATABASE_TYPE=postgresql)
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=novel_parser
POSTGRES_USER=novel_parser
POSTGRES_PASSWORD=password
POSTGRES_SCHEMA=public

# Application Configuration
API_HOST=0.0.0.0
API_PORT=5001
DOCS_DIR=docs
